import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../enums/user_role.dart';
import '../services/user_management_service.dart';

class UserDetailScreen extends StatefulWidget {
  final UserModel user;

  const UserDetailScreen({
    super.key,
    required this.user,
  });

  @override
  State<UserDetailScreen> createState() => _UserDetailScreenState();
}

class _UserDetailScreenState extends State<UserDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<PostModel> _userPosts = [];
  List<ProductModel> _userProducts = [];
  Map<String, int> _userStats = {};
  bool _isLoadingPosts = true;
  bool _isLoadingProducts = true;
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    await Future.wait([
      _loadUserPosts(),
      _loadUserProducts(),
      _loadUserStats(),
    ]);
  }

  Future<void> _loadUserPosts() async {
    try {
      final posts = await UserManagementService.getUserPosts(widget.user.id);
      setState(() {
        _userPosts = posts;
        _isLoadingPosts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPosts = false;
      });
    }
  }

  Future<void> _loadUserProducts() async {
    try {
      final products = await UserManagementService.getUserProducts(widget.user.id);
      setState(() {
        _userProducts = products;
        _isLoadingProducts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProducts = false;
      });
    }
  }

  Future<void> _loadUserStats() async {
    try {
      final stats = await UserManagementService.getUserStatistics(widget.user.id);
      setState(() {
        _userStats = stats;
        _isLoadingStats = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingStats = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.user.displayNameOrUsername),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          PopupMenuButton<String>(
            onSelected: _handleAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: widget.user.isActive ? 'deactivate' : 'activate',
                child: ListTile(
                  leading: Icon(widget.user.isActive ? Icons.block : Icons.check_circle),
                  title: Text(widget.user.isActive ? 'Deactivate' : 'Activate'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              PopupMenuItem(
                value: widget.user.isVerified ? 'unverify' : 'verify',
                child: ListTile(
                  leading: Icon(widget.user.isVerified ? Icons.verified_user : Icons.verified),
                  title: Text(widget.user.isVerified ? 'Remove Verification' : 'Verify User'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              if (widget.user.role != UserRole.admin)
                const PopupMenuItem(
                  value: 'change_role',
                  child: ListTile(
                    leading: Icon(Icons.admin_panel_settings),
                    title: Text('Change Role'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // User Profile Header
          _buildUserHeader(),
          
          // Tab Bar
          TabBar(
            controller: _tabController,
            tabs: const [
              Tab(text: 'Overview', icon: Icon(Icons.info_outline)),
              Tab(text: 'Posts', icon: Icon(Icons.post_add)),
              Tab(text: 'Products', icon: Icon(Icons.store)),
            ],
          ),
          
          // Tab Views
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildPostsTab(),
                _buildProductsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Avatar
          CircleAvatar(
            radius: 40,
            backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
            backgroundImage: widget.user.profileImageUrl != null
                ? NetworkImage(widget.user.profileImageUrl!)
                : null,
            child: widget.user.profileImageUrl == null
                ? Text(
                    widget.user.displayName.isNotEmpty
                        ? widget.user.displayName[0].toUpperCase()
                        : widget.user.email[0].toUpperCase(),
                    style: const TextStyle(
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  )
                : null,
          ),
          
          const SizedBox(width: AppConstants.paddingLarge),
          
          // User Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.user.displayNameOrUsername,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingSmall),
                    _buildRoleBadge(widget.user.role),
                    if (widget.user.isVerified) ...[
                      const SizedBox(width: AppConstants.paddingSmall),
                      const Icon(
                        Icons.verified,
                        size: 20,
                        color: AppConstants.successColor,
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  widget.user.email,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppConstants.paddingSmall,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: widget.user.isActive
                            ? AppConstants.successColor.withOpacity(0.1)
                            : AppConstants.errorColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
                      ),
                      child: Text(
                        widget.user.isActive ? 'Active' : 'Inactive',
                        style: TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: widget.user.isActive
                              ? AppConstants.successColor
                              : AppConstants.errorColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: AppConstants.paddingMedium),
                    Text(
                      'Joined: ${widget.user.formattedJoinDate}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppConstants.textHintColor,
                      ),
                    ),
                  ],
                ),
                if (widget.user.bio != null && widget.user.bio!.isNotEmpty) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    widget.user.bio!,
                    style: Theme.of(context).textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRoleBadge(UserRole role) {
    Color color;
    switch (role) {
      case UserRole.admin:
        color = AppConstants.errorColor;
        break;
      case UserRole.reseller:
        color = AppConstants.warningColor;
        break;
      case UserRole.user:
      default:
        color = AppConstants.primaryColor;
    }

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingSmall,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusSmall),
      ),
      child: Text(
        role.displayName,
        style: TextStyle(
          fontSize: AppConstants.fontSizeSmall,
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConstants.paddingLarge),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Statistics Cards
          if (!_isLoadingStats) _buildStatsGrid(),
          
          const SizedBox(height: AppConstants.paddingLarge),
          
          // User Details
          _buildUserDetails(),
        ],
      ),
    );
  }

  Widget _buildStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      childAspectRatio: 2,
      crossAxisSpacing: AppConstants.paddingMedium,
      mainAxisSpacing: AppConstants.paddingMedium,
      children: [
        _buildStatCard('Posts', _userStats['posts'] ?? 0, Icons.post_add),
        _buildStatCard('Products', _userStats['products'] ?? 0, Icons.store),
        _buildStatCard('Followers', _userStats['followers'] ?? 0, Icons.people),
        _buildStatCard('Following', _userStats['following'] ?? 0, Icons.person_add),
      ],
    );
  }

  Widget _buildStatCard(String title, int value, IconData icon) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: AppConstants.primaryColor),
            const SizedBox(height: AppConstants.paddingSmall),
            Text(
              value.toString(),
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryColor,
              ),
            ),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'User Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            _buildDetailRow('User ID', widget.user.id),
            _buildDetailRow('Username', widget.user.username),
            _buildDetailRow('Display Name', widget.user.displayName),
            _buildDetailRow('Email', widget.user.email),
            _buildDetailRow('Mobile', widget.user.mobile ?? 'Not provided'),
            _buildDetailRow('Role', widget.user.role.displayName),
            _buildDetailRow('Status', widget.user.isActive ? 'Active' : 'Inactive'),
            _buildDetailRow('Verified', widget.user.isVerified ? 'Yes' : 'No'),
            _buildDetailRow('Joined', widget.user.formattedJoinDate),
            if (widget.user.gender != null)
              _buildDetailRow('Gender', widget.user.gender!),
            if (widget.user.country != null)
              _buildDetailRow('Country', widget.user.country!),
            if (widget.user.address != null)
              _buildDetailRow('Address', widget.user.address!),

            // Referrer Information Section
            const SizedBox(height: AppConstants.paddingLarge),
            Text(
              'Referrer Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConstants.paddingMedium),
            _buildReferrerInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPostsTab() {
    if (_isLoadingPosts) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_userPosts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.post_add, size: 64, color: AppConstants.textHintColor),
            SizedBox(height: AppConstants.paddingMedium),
            Text('No posts found'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _userPosts.length,
      itemBuilder: (context, index) {
        final post = _userPosts[index];
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          child: ListTile(
            title: Text(post.content),
            subtitle: Text('Created: ${post.formattedCreatedAt}'),
            trailing: Icon(
              post.isActive ? Icons.visibility : Icons.visibility_off,
              color: post.isActive ? AppConstants.successColor : AppConstants.errorColor,
            ),
          ),
        );
      },
    );
  }

  Widget _buildProductsTab() {
    if (_isLoadingProducts) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_userProducts.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.store, size: 64, color: AppConstants.textHintColor),
            SizedBox(height: AppConstants.paddingMedium),
            Text('No products found'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      itemCount: _userProducts.length,
      itemBuilder: (context, index) {
        final product = _userProducts[index];
        return Card(
          margin: const EdgeInsets.only(bottom: AppConstants.paddingMedium),
          child: ListTile(
            leading: product.imageUrls.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      product.imageUrls.first,
                      width: 50,
                      height: 50,
                      fit: BoxFit.cover,
                    ),
                  )
                : Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: AppConstants.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.image),
                  ),
            title: Text(product.title),
            subtitle: Text('Price: \$${product.price.toStringAsFixed(2)}'),
            trailing: Icon(
              product.isAvailable ? Icons.check_circle : Icons.cancel,
              color: product.isAvailable ? AppConstants.successColor : AppConstants.errorColor,
            ),
          ),
        );
      },
    );
  }

  void _handleAction(String action) async {
    try {
      switch (action) {
        case 'activate':
        case 'deactivate':
          final isActive = action == 'activate';
          await UserManagementService.toggleUserActiveStatus(widget.user.id, isActive);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('User ${isActive ? 'activated' : 'deactivated'} successfully'),
                backgroundColor: AppConstants.successColor,
              ),
            );
            Navigator.of(context).pop(); // Go back to refresh the list
          }
          break;

        case 'verify':
        case 'unverify':
          final isVerified = action == 'verify';
          await UserManagementService.toggleUserVerifiedStatus(widget.user.id, isVerified);
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('User ${isVerified ? 'verified' : 'unverified'} successfully'),
                backgroundColor: AppConstants.successColor,
              ),
            );
            Navigator.of(context).pop(); // Go back to refresh the list
          }
          break;

        case 'reset_password':
          _showPasswordResetDialog();
          break;

        case 'change_role':
          _showChangeRoleDialog();
          break;

        case 'delete':
          _showDeleteUserDialog();
          break;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to perform action: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _showPasswordResetDialog() {
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    bool obscureNewPassword = true;
    bool obscureConfirmPassword = true;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Change Password'),
          content: SizedBox(
            width: 300,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Change password for ${widget.user.displayNameOrUsername}',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: newPasswordController,
                  obscureText: obscureNewPassword,
                  decoration: InputDecoration(
                    labelText: 'New Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureNewPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscureNewPassword = !obscureNewPassword;
                        });
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                TextField(
                  controller: confirmPasswordController,
                  obscureText: obscureConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'Confirm Password',
                    border: const OutlineInputBorder(),
                    suffixIcon: IconButton(
                      icon: Icon(
                        obscureConfirmPassword ? Icons.visibility : Icons.visibility_off,
                      ),
                      onPressed: () {
                        setState(() {
                          obscureConfirmPassword = !obscureConfirmPassword;
                        });
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newPassword = newPasswordController.text.trim();
                final confirmPassword = confirmPasswordController.text.trim();

                if (newPassword.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Please enter a new password'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                  return;
                }

                if (newPassword.length < 6) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Password must be at least 6 characters'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                  return;
                }

                if (newPassword != confirmPassword) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Passwords do not match'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();
                try {
                  await UserManagementService.changeUserPassword(widget.user.id, newPassword);
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Password changed successfully for ${widget.user.displayNameOrUsername}'),
                        backgroundColor: AppConstants.successColor,
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Failed to change password: $e'),
                        backgroundColor: AppConstants.errorColor,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Change Password'),
            ),
          ],
        ),
      ),
    );
  }

  void _showChangeRoleDialog() {
    UserRole selectedRole = widget.user.role;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Change User Role'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: UserRole.values.map((role) {
              return RadioListTile<UserRole>(
                title: Text(role.displayName),
                value: role,
                groupValue: selectedRole,
                onChanged: (value) {
                  setState(() {
                    selectedRole = value!;
                  });
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: selectedRole == widget.user.role
                  ? null
                  : () async {
                      Navigator.of(context).pop();
                      try {
                        await UserManagementService.updateUserRole(widget.user.id, selectedRole);
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('User role changed to ${selectedRole.displayName}'),
                              backgroundColor: AppConstants.successColor,
                            ),
                          );
                          Navigator.of(context).pop(); // Go back to refresh the list
                        }
                      } catch (e) {
                        if (mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Failed to change user role: $e'),
                              backgroundColor: AppConstants.errorColor,
                            ),
                          );
                        }
                      }
                    },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppConstants.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Change Role'),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteUserDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text(
          'Are you sure you want to delete ${widget.user.displayNameOrUsername}? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              try {
                await UserManagementService.deleteUser(widget.user.id);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('${widget.user.displayNameOrUsername} deleted successfully'),
                      backgroundColor: AppConstants.successColor,
                    ),
                  );
                  Navigator.of(context).pop(); // Go back to refresh the list
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete user: $e'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppConstants.errorColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Widget _buildReferrerInfo() {
    if (widget.user.referrerId == null || widget.user.referrerId!.isEmpty) {
      return _buildDetailRow('Referrer', 'No referrer');
    }

    return FutureBuilder<UserModel?>(
      future: UserManagementService.getUserById(widget.user.referrerId!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: AppConstants.paddingMedium),
            child: Center(
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
          );
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return Column(
            children: [
              _buildDetailRow('Referrer ID', widget.user.referrerId!),
              if (widget.user.referrerName != null)
                _buildDetailRow('Referrer Name', widget.user.referrerName!),
              if (widget.user.referrerMobile != null)
                _buildDetailRow('Referrer Mobile', widget.user.referrerMobile!),
              _buildDetailRow('Referrer Status', 'User not found or deleted'),
            ],
          );
        }

        final referrer = snapshot.data!;
        return Column(
          children: [
            _buildDetailRow('Referrer ID', referrer.id),
            _buildDetailRow('Referrer Name', referrer.displayNameOrUsername),
            _buildDetailRow('Referrer Username', '@${referrer.username}'),
            _buildDetailRow('Referrer Email', referrer.email),
            _buildDetailRow('Referrer Mobile', referrer.mobile ?? 'Not provided'),
            _buildDetailRow('Referrer Role', referrer.role.displayName),
            _buildDetailRow('Referrer Status', referrer.isActive ? 'Active' : 'Inactive'),
            if (referrer.country != null)
              _buildDetailRow('Referrer Country', referrer.country!),
            if (referrer.address != null)
              _buildDetailRow('Referrer Address', referrer.address!),
          ],
        );
      },
    );
  }
}
